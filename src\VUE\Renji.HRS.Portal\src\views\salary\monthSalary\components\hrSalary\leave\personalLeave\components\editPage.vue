<template>
  <div>
    <el-dialog :title="title" :visible="showDialog" width="90%" :top="'10vh'" :close-on-press-escape="false" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="dataModel" label-width="100px">
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>基础信息</span>
          </div>
          <el-row>
            <el-col :span="6">
              <el-form-item label="员工姓名" label-width="120px">
                <div>
                  <span>
                    {{ dataModel.employeeModel.empName }}
                  </span>
                  <el-button v-if="!isEdit" style="margin-left: 15px;" type="primary" title="选择员工" @click="selectEmployeeDialog">选 择</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="唯一码" label-width="120px">
                {{ dataModel.employeeModel.empUid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工号" label-width="120px">
                {{ dataModel.employeeModel.empCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别" label-width="120px">
                {{ dataModel.employeeModel.genderDesc }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工龄" label-width="120px">
                {{ dataModel.employeeModel.societyAge }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card style="margin-top: 3px;">
          <div slot="header">
            <span>病产假信息</span>
          </div>
          <el-row>
            <el-col :span="8">
              <el-row>
                <el-form-item label="上下班交通费基数" prop="backPayDeduction" label-width="120px">
                  <span class="table-data">{{ dataModel.commutingAllowance | formatMoney2 }}</span>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="岗资+薪资+岗位津贴" prop="backPayDeduction" label-width="120px" style="white-space: nowrap;">
                  <span class="table-data">{{ dataModel.basePay | formatMoney2 }}</span>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="扣除比例" label-width="120px">
                  {{ formatDiscountRatio(dataModel.discountRatio) }}
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="计算请假类型" prop="EnumSalaryLeaveType" label-width="120px">
                  {{ dataModel.enumSalaryLeaveTypeDesc }}
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="实际请假类型" prop="EnumSalaryLeaveType" label-width="120px">
                  <el-select v-model="dataModel.enumSalaryLeaveType" clearable placeholder="请假类型" style="width: 60%" class="filter-item" @change="setSalaryLeaveType">
                    <el-option v-for="item in leaveDaysTypeList" :key="item.value" :label="item.desc" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-row>


            </el-col>
            <el-col :span="16">
              <el-row>
                <el-form-item label-width="120px" style="width: 90%;">
                  <div class="table-row">
                    <span class="table-header">类型</span>
                    <span class="table-header">天数</span>
                    <span class="table-header">上下班交通费</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h4')&&dataModel.h4!=0" class="table-row">
                    <span class="table-data">产假</span>
                    <span class="table-data">{{ dataModel.h4 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h4 | formatMoney2 }}</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h2')&&dataModel.h2!=0" class="table-row">
                    <span class="table-data">病假</span>
                    <span class="table-data">{{ dataModel.h2 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h2 | formatMoney2 }}</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h3')&&dataModel.h3!=0" class="table-row">
                    <span class="table-data">事假</span>
                    <span class="table-data">{{ dataModel.h3 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h3 | formatMoney2 }}</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h5')&&dataModel.h5!=0" class="table-row">
                    <span class="table-data">哺乳假</span>
                    <span class="table-data">{{ dataModel.h5 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h5 | formatMoney2 }}</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h6')&&dataModel.h6!=0" class="table-row">
                    <span class="table-data">探亲假</span>
                    <span class="table-data">{{ dataModel.h6 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h6 | formatMoney2 }}</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h7')&&dataModel.h7!=0" class="table-row">
                    <span class="table-data">计生假</span>
                    <span class="table-data">{{ dataModel.h7 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h7 | formatMoney2 }}</span>
                  </div>
                  <div v-if="dataModel.hasOwnProperty('h8')&&dataModel.h8!=0" class="table-row">
                    <span class="table-data">婚丧假</span>
                    <span class="table-data">{{ dataModel.h8 | formatMoney2 }}</span>
                    <span class="table-data">{{ dataModel.commutingAllowance/maxDaysOfMonth*dataModel.h8 | formatMoney2 }}</span>
                  </div>
                </el-form-item>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="备注" label-width="120px">
                <el-input v-model="dataModel.remark" type="textarea" :rows="3" maxlength="300" placeholder="备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="btnSaveLoading" @click="saveDialog">保 存</el-button>
      </div>
    </el-dialog>

    <selectUserComponent ref="selectEmployee" @selectRow="setEmployee" />

  </div>
</template>
<script>
import salaryApi from '@/api/salary'
import sysManageApi from '@/api/sysManage'
import selectUserComponent from '@/views/salary/employeeSalary/components/selectUser'

export default {
  components: {
    selectUserComponent
  },
  data() {
    return {
      showDialog: false,
      title: '',
      rules: {
      },
      btnSaveLoading: false,
      leaveDaysTypeList: [],
      isEdit: false,
      maxDaysOfMonth: 0,
      salaryMonth: '',
      dataModel: {
        employeeModel: {}
      }
    }
  },
  methods: {
    initDialog(row) {
      if (!row) {
        this.title = '新增病产假'
        this.isEdit = false
      } else {
        this.title = '编辑病产假'
        this.isEdit = true
        this.getData(row.id)
      }
      this.dataModel.salaryId = this.salaryId
      this.showDialog = true
      this.loadLeaveDaysType()
      this.getMaxDaysOfMonth(this.salaryId)
    },
    getData(id) {
      salaryApi.getSalaryLeave({ id: id }).then(res => {
        if (res.succeed) {
          this.dataModel = res.data
          this.setEmployee(res.data.employee)
        }
      }).catch(res => {
      })
    },
    getMaxDaysOfMonth(salaryId) {
      salaryApi.getSalary({ id: salaryId }).then(res => {
        if (res.succeed) {
          const salaryData = res.data;
          const recordMonth = salaryData.month;
          const [year, month] = recordMonth.split('-').map(Number);
          
          // 计算两个月前的月份和年份
          let targetMonth = month - 2;
          let targetYear = year;
          
          // 处理月份为负数的情况（跨年）
          if (targetMonth <= 0) {
            targetMonth += 12; // 调整为正数月份
            targetYear -= 1;   // 年份减1
          }
          
          // 获取目标月份的最后一天
          const daysInTargetMonth = new Date(targetYear, targetMonth, 0).getDate();
          
          this.salaryMonth = res.data
          this.maxDaysOfMonth = daysInTargetMonth;
        }
      }).catch(res => {
      })
    },
    selectEmployeeDialog() {
      this.$refs.selectEmployee.loadTree()
      this.$refs.selectEmployee.showEmployee = true
    },
    saveDialog() {

      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.btnSaveLoading = true
          if (!this.isEdit) {
            salaryApi.addSalaryLeave(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '添加成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          } else {
            salaryApi.updateSalaryLeave(this.dataModel).then(res => {
              if (res.succeed) {
                this.$message({ message: '修改成功', type: 'success' })
                this.btnSaveLoading = false
                this.$emit('refreshData')
                this.closeDialog()
              }
            }).catch(res => {
              this.btnSaveLoading = false
            })
          }
        }
      })
    },
    closeDialog() {
      this.dataModel = {
        employeeModel: {}
      }
      this.showDialog = false
      this.$refs.dataForm.resetFields()
    },
    setEmployee(emp) {
      this.dataModel.employeeId = emp.id
      this.dataModel.salaryId = this.salaryId
      this.$set(this.dataModel, 'employeeModel',
        {
          employeeId: emp.id,
          empUid: emp.uid,
          empCode: emp.empCode,
          empName: emp.displayName,
          genderDesc: emp.enumGenderDesc,
          empDept: emp.deptName,
          hospitalAreaNameText: emp.hospitalAreaNameText,
          identityNumber: emp.identityNumber,
          hireStyleName: emp.employeeHR.hireStyleName,
          leaveStyleName: emp.employeeHR.leaveStyleName,
          deadDate: emp.employeeHR.deadDate,
          societyAge: emp.employeeHR.societyAge
        })
    },
    
    setSalaryLeaveType() {
      switch (this.dataModel.enumSalaryLeaveType) {
        case 1: // 产假A
          this.dataModel.discountRatio = 1
          break
        case 2: // 产假B
          this.dataModel.discountRatio = 1
          break
        case 3: // 产假C
          this.dataModel.discountRatio = 1
          break
        case 4: // 产假D
          this.dataModel.discountRatio = 1
          break
        case 5: // 产假E
          this.dataModel.discountRatio = 1
          break
        case 6: // 产假F
          this.dataModel.discountRatio = 1
          break
        case 7: // 病假类型一（病假<=62天）
          this.dataModel.discountRatio = null
          break
        case 8: // 病假类型二（病假63~180天工龄10年以下）
          this.dataModel.discountRatio = 0.1
          break
        case 9: // 病假类型二（病假63~180天工龄10年及以上）
          this.dataModel.discountRatio = null
          break
        case 10: // 病假类型三（病假>=181天工龄10年以下）
          this.dataModel.discountRatio = 0.3
          break
        case 11: // 病假类型三（病假>=181天工龄10年及以上）
          this.dataModel.discountRatio = 0.2
          break
        case 12: // 哺乳假
          this.dataModel.discountRatio = 0.2
          break
        case 13: // 公假
          this.dataModel.discountRatio = null
          break
        case 14: // 事假一（累计<=20天或连续<=10）
          this.dataModel.discountRatio = null
          break
        case 15: // 事假二（连续>10天或累计>20天）
          this.dataModel.discountRatio = 0.3
          break
        case 16: // 事假三（累计>30天）
          this.dataModel.discountRatio = 0.5
          break
        case 17: // 事假四（累计>60天）
          this.dataModel.discountRatio = 1
          break
        default:
          break
      }
    },
    loadLeaveDaysType() {
      sysManageApi.getEnumInfos({ enumType: 'SalaryLeaveType' }).then(result => {
        this.leaveDaysTypeList = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    leaveDaysChange() {
      if (this.dataModel.leaveDays > 0 &&
      this.dataModel.enumLeaveDaysType > 0 &&
      this.dataModel.salaryId != null &&
      this.dataModel.employeeId != null
    ) {
        salaryApi.calculateSalaryLeave(this.dataModel).then(res => {
              if (res.succeed) {
                this.$set(this.dataModel, 'discountRate', res.data.discountRate)
                this.$set(this.dataModel, 'commutingAllowance', res.data.commutingAllowance)
                this.$set(this.dataModel, 'enumSalaryLeaveType', res.data.enumSalaryLeaveType)
                this.$set(this.dataModel, 'continuousLeaveDays', res.data.continuousLeaveDays)
              }
            }).catch(res => {
            })
      }
    },
    // 格式化折扣比例为百分比显示
    formatDiscountRatio(ratio) {
      if (ratio === null || ratio === undefined) {
        return '-'
      }
      if (ratio === 0) {
        return '0%'
      }
      return (ratio * 100).toFixed(0) + '%'
    }
  }
}
</script>

<style>
</style>
