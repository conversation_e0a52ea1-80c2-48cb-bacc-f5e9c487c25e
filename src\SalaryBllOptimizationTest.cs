using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using Renji.JHR.Bll;
using Renji.JHR.Entities;

namespace Renji.JHR.Tests
{
    /// <summary>
    /// SalaryBll 性能优化测试类
    /// 用于验证 CalculateSalary 方法的性能优化效果
    /// </summary>
    public class SalaryBllOptimizationTest
    {
        /// <summary>
        /// 测试薪资计算性能
        /// </summary>
        /// <param name="salaryId">薪资ID</param>
        /// <returns>性能测试结果</returns>
        public static PerformanceTestResult TestSalaryCalculationPerformance(Guid salaryId)
        {
            var result = new PerformanceTestResult();
            var stopwatch = new Stopwatch();

            try
            {
                // 创建 SalaryBll 实例（需要根据实际的依赖注入配置调整）
                var salaryBll = CreateSalaryBllInstance();

                // 预热
                Console.WriteLine("开始预热...");
                stopwatch.Start();
                var warmupResult = salaryBll.CalculateSalary(salaryId);
                stopwatch.Stop();
                result.WarmupTime = stopwatch.ElapsedMilliseconds;
                Console.WriteLine($"预热完成，耗时: {result.WarmupTime}ms");

                // 正式测试
                Console.WriteLine("开始性能测试...");
                stopwatch.Restart();
                var testResult = salaryBll.CalculateSalary(salaryId);
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.ElapsedMilliseconds;
                result.Success = testResult.Succeed;
                result.ErrorMessage = testResult.Succeed ? null : testResult.Message;

                Console.WriteLine($"性能测试完成:");
                Console.WriteLine($"  执行时间: {result.ExecutionTime}ms");
                Console.WriteLine($"  执行结果: {(result.Success ? "成功" : "失败")}");
                if (!result.Success)
                {
                    Console.WriteLine($"  错误信息: {result.ErrorMessage}");
                }

                // 内存使用情况
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                result.MemoryUsed = GC.GetTotalMemory(false);
                Console.WriteLine($"  内存使用: {result.MemoryUsed / 1024 / 1024:F2}MB");

            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 创建 SalaryBll 实例
        /// 注意：这里需要根据实际的依赖注入配置进行调整
        /// </summary>
        /// <returns>SalaryBll 实例</returns>
        private static SalaryBll CreateSalaryBllInstance()
        {
            // 这里需要根据实际的依赖注入配置来创建实例
            // 示例代码，实际使用时需要调整
            
            // 方式1：如果有服务容器
            // var serviceProvider = GetServiceProvider();
            // return serviceProvider.GetRequiredService<SalaryBll>();

            // 方式2：直接创建（需要传入必要的依赖）
            // return new SalaryBll(operatorUser);

            // 方式3：使用默认构造函数（如果支持）
            throw new NotImplementedException("请根据实际的依赖注入配置实现 SalaryBll 实例创建");
        }

        /// <summary>
        /// 批量性能测试
        /// </summary>
        /// <param name="salaryIds">薪资ID列表</param>
        /// <param name="iterations">每个ID的测试次数</param>
        /// <returns>批量测试结果</returns>
        public static BatchPerformanceTestResult BatchTest(List<Guid> salaryIds, int iterations = 3)
        {
            var batchResult = new BatchPerformanceTestResult();
            var allResults = new List<PerformanceTestResult>();

            Console.WriteLine($"开始批量性能测试，共 {salaryIds.Count} 个薪资ID，每个测试 {iterations} 次");

            foreach (var salaryId in salaryIds)
            {
                Console.WriteLine($"\n测试薪资ID: {salaryId}");
                var salaryResults = new List<PerformanceTestResult>();

                for (int i = 0; i < iterations; i++)
                {
                    Console.WriteLine($"  第 {i + 1} 次测试:");
                    var result = TestSalaryCalculationPerformance(salaryId);
                    salaryResults.Add(result);
                    allResults.Add(result);
                }

                // 计算该薪资ID的平均性能
                var avgTime = salaryResults.Where(r => r.Success).Average(r => r.ExecutionTime);
                Console.WriteLine($"  平均执行时间: {avgTime:F2}ms");
            }

            // 计算总体统计
            var successfulResults = allResults.Where(r => r.Success).ToList();
            if (successfulResults.Any())
            {
                batchResult.TotalTests = allResults.Count;
                batchResult.SuccessfulTests = successfulResults.Count;
                batchResult.AverageExecutionTime = successfulResults.Average(r => r.ExecutionTime);
                batchResult.MinExecutionTime = successfulResults.Min(r => r.ExecutionTime);
                batchResult.MaxExecutionTime = successfulResults.Max(r => r.ExecutionTime);
                batchResult.AverageMemoryUsed = successfulResults.Average(r => r.MemoryUsed);
            }

            Console.WriteLine($"\n批量测试完成:");
            Console.WriteLine($"  总测试次数: {batchResult.TotalTests}");
            Console.WriteLine($"  成功次数: {batchResult.SuccessfulTests}");
            Console.WriteLine($"  成功率: {(double)batchResult.SuccessfulTests / batchResult.TotalTests * 100:F1}%");
            Console.WriteLine($"  平均执行时间: {batchResult.AverageExecutionTime:F2}ms");
            Console.WriteLine($"  最短执行时间: {batchResult.MinExecutionTime}ms");
            Console.WriteLine($"  最长执行时间: {batchResult.MaxExecutionTime}ms");
            Console.WriteLine($"  平均内存使用: {batchResult.AverageMemoryUsed / 1024 / 1024:F2}MB");

            return batchResult;
        }
    }

    /// <summary>
    /// 性能测试结果
    /// </summary>
    public class PerformanceTestResult
    {
        public long WarmupTime { get; set; }
        public long ExecutionTime { get; set; }
        public long MemoryUsed { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 批量性能测试结果
    /// </summary>
    public class BatchPerformanceTestResult
    {
        public int TotalTests { get; set; }
        public int SuccessfulTests { get; set; }
        public double AverageExecutionTime { get; set; }
        public long MinExecutionTime { get; set; }
        public long MaxExecutionTime { get; set; }
        public double AverageMemoryUsed { get; set; }
    }
}
